---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry, getCollection } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about } = homepageContent.data;

// Get latest portfolio projects for preview
const portfolioProjects = await getCollection('portfolio');
const featuredProjects = portfolioProjects
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3);
---

<Layout title="Nob Hokleng | Software Developer & System Architect">
  <!-- Hero Section -->
  <section class="hero relative min-h-screen flex items-center justify-center overflow-hidden" role="banner">
    <!-- Background with improved gradients -->
    <div class="absolute inset-0 bg-gradient-to-br from-light via-white to-gray-50"></div>
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/5 via-transparent to-accent/5"></div>
    
    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-secondary/10 to-primary/10 rounded-full blur-3xl animate-pulse"></div>
    </div>
    
    <div class="container mx-auto px-5 max-w-6xl relative z-10 py-20">
      <div class="text-center">
        
        <!-- Main heading with enhanced typography -->
        <h1 class="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold font-heading mb-6 leading-tight">
          <span class="block bg-gradient-to-r from-secondary via-primary to-secondary bg-clip-text text-transparent" set:html={hero.headline}></span>
        </h1>
        
        <h2 class="text-xl sm:text-2xl lg:text-3xl text-primary mb-8 font-semibold max-w-4xl mx-auto leading-relaxed">
          {hero.subheadline}
        </h2>
        
        <p class="text-lg sm:text-xl max-w-3xl mx-auto mb-12 text-text/80 leading-relaxed">
          {hero.description}
        </p>
        
        <!-- Enhanced highlights with better spacing -->
        <div class="hero-highlights grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
          {hero.highlights.map((highlight) => (
            <div class="highlight-item group flex flex-col items-center gap-3 p-5 bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:bg-white/90">
              <span class="text-2xl group-hover:scale-110 transition-transform duration-300">{highlight.icon}</span>
              <span class="font-semibold text-text text-sm text-center leading-tight">{highlight.label}</span>
            </div>
          ))}
        </div>
        
        <!-- Enhanced CTA buttons -->
        <div class="cta-buttons flex justify-center gap-6 flex-col sm:flex-row">
          <a href={hero.primaryCTA.url} class="group btn-primary inline-flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-lg" aria-label="View my portfolio projects">
            {hero.primaryCTA.text}
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a href={hero.secondaryCTA.url} class="group btn-secondary inline-flex items-center justify-center gap-3 px-8 py-4 border-2 border-primary text-primary rounded-2xl font-semibold hover:bg-primary hover:text-white transition-all duration-300 hover:-translate-y-1 text-lg backdrop-blur-sm bg-white/50" aria-label="Get in touch with me">
            {hero.secondaryCTA.text}
            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </a>
        </div>
        
        <!-- Scroll indicator -->
        <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg class="w-6 h-6 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about py-24 bg-white" role="main">
    <div class="container mx-auto px-5 max-w-6xl">
      <h2 class="section-title text-4xl font-bold text-center mb-12 font-heading relative">
        About Me
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-15 items-start">
        <div class="about-text space-y-6">
          <p class="text-lg font-medium text-secondary leading-relaxed">
            {about.openingLine}
          </p>
          {about.mainContent.map((paragraph) => (
            <p class="text-base leading-relaxed text-text">
              {paragraph}
            </p>
          ))}
        </div>
          
          <div class="experience-highlights mt-8">
            <h3 class="text-xl font-bold text-secondary mb-5 font-heading">Experience Highlights</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Designed and implemented scalable backend systems</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Built high-performance APIs serving thousands of requests</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Implemented DevOps practices and CI/CD pipelines</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Worked with cloud platforms and containerization</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Mentored team members and led technical initiatives</span>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- Technical Skills -->
        <div class="skills-section">
          <div class="bg-gradient-to-br from-white via-white to-gray-50/50 p-8 lg:p-10 rounded-3xl shadow-xl border border-white/50 backdrop-blur-sm">
            <h3 class="text-2xl font-bold text-secondary mb-8 text-center font-heading flex items-center justify-center gap-3">
              <span class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center text-white text-sm">⚡</span>
              Technical Skills
            </h3>
            
            <div class="grid grid-cols-1 gap-6">
              <!-- Backend Development -->
              <div class="skill-category group bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-primary/10">
                <h4 class="text-lg font-semibold text-primary mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center text-white text-xs">🔧</span>
                  Backend Development
                </h4>
                <div class="flex flex-wrap gap-2">
                  {["Java & Spring Boot", "Node.js & Express", "Python & FastAPI", "RESTful APIs", "GraphQL", "Microservices"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-primary/10 to-secondary/10 text-primary text-sm font-medium rounded-full border border-primary/20 hover:bg-gradient-to-r hover:from-primary/20 hover:to-secondary/20 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <!-- System Architecture -->
              <div class="skill-category group bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-secondary/10">
                <h4 class="text-lg font-semibold text-secondary mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-secondary to-primary rounded-lg flex items-center justify-center text-white text-xs">🏗️</span>
                  System Architecture
                </h4>
                <div class="flex flex-wrap gap-2">
                  {["Event-Driven Systems", "Database Design", "Caching Strategies", "Load Balancing", "Message Queues", "API Gateway"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-secondary/10 to-primary/10 text-secondary text-sm font-medium rounded-full border border-secondary/20 hover:bg-gradient-to-r hover:from-secondary/20 hover:to-primary/20 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <!-- DevOps & Cloud -->
              <div class="skill-category group bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-accent/10">
                <h4 class="text-lg font-semibold text-accent mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-accent to-orange-500 rounded-lg flex items-center justify-center text-white text-xs">☁️</span>
                  DevOps & Cloud
                </h4>
                <div class="flex flex-wrap gap-2">
                  {["Docker & Kubernetes", "AWS & Azure", "CI/CD Pipelines", "Terraform", "Monitoring & Logging", "Infrastructure as Code"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-accent/10 to-orange-500/10 text-accent text-sm font-medium rounded-full border border-accent/20 hover:bg-gradient-to-r hover:from-accent/20 hover:to-orange-500/20 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            
            <!-- Call to action -->
            <div class="mt-8 text-center">
              <a href="/resume" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-sm">
                View Full Resume
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="portfolio py-24 bg-gradient-to-br from-light via-white to-gray-50/50 relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <!-- Background elements -->
    <div class="absolute inset-0">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-accent/5 to-transparent rounded-full blur-3xl"></div>
    </div>
    
    <div class="container mx-auto px-5 max-w-6xl relative z-10">
      <div class="text-center mb-16">
        <h2 id="portfolio-title" class="section-title text-4xl lg:text-5xl font-bold font-heading mb-4 bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
          Featured Projects
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-primary to-accent rounded mx-auto mb-6"></div>
        <p class="text-lg text-text/70 max-w-2xl mx-auto mb-8">
          A showcase of scalable systems and innovative solutions I've built
        </p>
      </div>
      
      <!-- Portfolio Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12" role="list">
        {featuredProjects.length > 0 ? (
          featuredProjects.map((project) => (
            <ProjectCard 
              title={project.data.title}
              description={project.data.problem + " " + project.data.solution}
              tags={project.data.technologies.slice(0, 3)}
              slug={project.slug}
            />
          ))
        ) : (
          <!-- Fallback projects if no portfolio content exists -->
          <>
            <ProjectCard 
              title="E-Commerce Platform Backend"
              description="Scalable microservices architecture for high-traffic e-commerce platform. Built with modern technologies to handle thousands of concurrent users and process millions of transactions."
              tags={["Java", "Spring Boot", "Microservices"]}
            />
            <ProjectCard 
              title="Real-time Analytics System"
              description="Event-driven system processing millions of events daily. Provides real-time insights and analytics with low-latency data processing and visualization."
              tags={["Node.js", "Apache Kafka", "Redis"]}
            />
            <ProjectCard 
              title="Cloud Infrastructure Platform"
              description="Automated CI/CD pipeline and infrastructure as code implementation. Streamlined deployment processes with monitoring, logging, and automated scaling."
              tags={["Docker", "Kubernetes", "AWS"]}
            />
          </>
        )}
      </div>
      
      <!-- Portfolio CTA -->
      <div class="text-center">
        <a href="/portfolio" class="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-lg">
          View All Projects
          <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
  
  <!-- Call to Action Section -->
  <section class="cta-section py-24 bg-gradient-to-r from-secondary to-primary relative overflow-hidden">
    <div class="absolute inset-0 bg-black/10"></div>
    <div class="container mx-auto px-5 max-w-4xl relative z-10 text-center">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6 font-heading">
        Ready to Build Something Amazing?
      </h2>
      <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
        Let's discuss your next project and how I can help you build scalable, high-performance solutions.
      </p>
      <div class="flex justify-center gap-6 flex-col sm:flex-row">
        <a href="/contact" class="group inline-flex items-center justify-center gap-3 px-8 py-4 bg-white text-primary rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-lg">
          Start a Conversation
          <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </a>
        <a href="/resume" class="group inline-flex items-center justify-center gap-3 px-8 py-4 border-2 border-white text-white rounded-2xl font-semibold hover:bg-white hover:text-primary transition-all duration-300 hover:-translate-y-1 text-lg backdrop-blur-sm">
          Download Resume
          <svg class="w-5 h-5 group-hover:translate-y-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</Layout> 